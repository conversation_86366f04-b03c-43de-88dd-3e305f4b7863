import torch
import math
from tqdm import tqdm
import comfy.utils

# This list defines the available methods for the initial frame upscale.
UPSCALE_METHODS = ["nearest-exact", "bilinear", "area", "bicubic", "lanczos", "custom model"]

def calculate_optimal_dimensions(width, height, tile_width, tile_height, min_overlap_perc, scale_factor):
    """
    Calculates the final upscaled dimensions and grid layout to ensure it perfectly
    contains the requested scale factor while respecting tile and overlap settings.
    """
    if scale_factor < 1.0:
        # Simple downscaling calculation
        target_width = int(width * scale_factor)
        target_height = int(height * scale_factor)
        overlap_x = int(tile_width * min_overlap_perc)
        overlap_y = int(tile_height * min_overlap_perc)
        grid_x = 1 if target_width <= tile_width else math.ceil((target_width - overlap_x) / (tile_width - overlap_x))
        grid_y = 1 if target_height <= tile_height else math.ceil((target_height - overlap_y) / (tile_height - overlap_y))
        grid_x, grid_y = max(1, grid_x), max(1, grid_y)
        final_width = (tile_width * grid_x) - (overlap_x * (grid_x - 1))
        final_height = (tile_height * grid_y) - (overlap_y * (grid_y - 1))
        final_width, final_height = max(final_width, target_width), max(final_height, target_height)
        return final_width, final_height, grid_x, grid_y, overlap_x, overlap_y

    # Upscaling calculation to find the best fit
    overlap = min_overlap_perc
    overlap_x, overlap_y = int(tile_width * overlap), int(tile_height * overlap)
    
    # Determine grid and final dimensions based on the smaller original dimension to maintain aspect ratio
    if width <= height:
        multiply_factor = math.ceil(scale_factor * width / tile_width) if tile_width > 0 else 1
        while True:
            grid_x = max(1, multiply_factor)
            upscaled_width = (tile_width * grid_x) - (overlap_x * (grid_x - 1))
            upscale_ratio = upscaled_width / width if width > 0 else scale_factor
            if upscale_ratio >= scale_factor: break
            multiply_factor += 1
        upscaled_height = int(height * upscale_ratio)
        grid_y = max(1, math.ceil((upscaled_height - overlap_y) / (tile_height - overlap_y)) if (tile_height - overlap_y) > 0 else 1)
        # Recalculate overlap_y to ensure perfect fit
        overlap_y = round((tile_height * grid_y - upscaled_height) / (grid_y - 1)) if grid_y > 1 else 0
    else:
        multiply_factor = math.ceil(scale_factor * height / tile_height) if tile_height > 0 else 1
        while True:
            grid_y = max(1, multiply_factor)
            upscaled_height = (tile_height * grid_y) - (overlap_y * (grid_y - 1))
            upscale_ratio = upscaled_height / height if height > 0 else scale_factor
            if upscale_ratio >= scale_factor: break
            multiply_factor += 1
        upscaled_width = int(width * upscale_ratio)
        grid_x = max(1, math.ceil((upscaled_width - overlap_x) / (tile_width - overlap_x)) if (tile_width - overlap_x) > 0 else 1)
        # Recalculate overlap_x to ensure perfect fit
        overlap_x = round((tile_width * grid_x - upscaled_width) / (grid_x - 1)) if grid_x > 1 else 0
        
    return upscaled_width, upscaled_height, grid_x, grid_y, overlap_x, overlap_y

def get_tile_coordinates(image_width, image_height, tile_width, tile_height, overlap_x, overlap_y, grid_x, grid_y):
    """
    Generates the (x, y) coordinates for each tile in a uniform grid.
    It ensures that tiles at the edges align perfectly with the image boundaries.
    """
    coords = []
    for row in range(grid_y):
        for col in range(grid_x):
            y_start = row * (tile_height - overlap_y)
            x_start = col * (tile_width - overlap_x)
            # Pin tiles to the bottom and right edges to avoid partial tiles
            if row == grid_y - 1:
                y_start = image_height - tile_height
            if col == grid_x - 1:
                x_start = image_width - tile_width
            coords.append((int(max(0, x_start)), int(max(0, y_start))))
    return coords

class SimplifiedGridTiler:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "images": ("IMAGE",),
                "upscale_by": ("FLOAT", {"default": 1.0, "min": 0.1, "max": 16.0, "step": 0.1, "display": "slider"}),
                "tile_width": ("INT", {"default": 512, "min": 64, "max": 4096, "step": 64}),
                "tile_height": ("INT", {"default": 512, "min": 64, "max": 4096, "step": 64}),
                "min_overlap_perc": ("FLOAT", {"default": 0.25, "min": 0.0, "max": 0.9, "step": 0.01, "display": "slider"}),
                "scaling_method": (UPSCALE_METHODS, {"default": "lanczos"}),
            },
            "optional": {
                "upscale_model": ("UPSCALE_MODEL",),
            }
        }

    RETURN_TYPES = ("IMAGE", "DAC_DATA")
    RETURN_NAMES = ("TILE_SEQUENCES", "dac_data")
    FUNCTION = "divide"
    CATEGORY = "⚫mimikry/Upscale Utils/Tiling"
    OUTPUT_IS_LIST = (True, False)

    def divide(self, images, upscale_by, tile_width, tile_height, min_overlap_perc, scaling_method, upscale_model=None):
        num_frames, original_height, original_width, _ = images.shape
        if scaling_method == "custom model" and upscale_model is None:
            raise ValueError("Error: 'custom model' was selected, but no upscale_model was provided.")

        # Calculate the final canvas size and grid configuration
        final_width, final_height, grid_x, grid_y, overlap_x, overlap_y = calculate_optimal_dimensions(
            original_width, original_height, tile_width, tile_height, min_overlap_perc, upscale_by
        )

        # This helper function handles the initial upscaling of each frame before tiling
        def upscale_frame(frame_tensor):
            if upscale_by != 1.0:
                frame_chw = frame_tensor.permute(0, 3, 1, 2)
                if scaling_method == "custom model" and upscale_by > 1.0:
                    # Use the provided custom upscale model
                    upscaled_tensor_chw = upscale_model(frame_chw)
                    # Resize to the final calculated dimensions for perfect tiling
                    resized_frame_chw = comfy.utils.common_upscale(upscaled_tensor_chw, final_width, final_height, "lanczos", "center")
                else:
                    # Use a standard scaling method
                    resized_frame_chw = comfy.utils.common_upscale(frame_chw, final_width, final_height, scaling_method, "center")
                return resized_frame_chw.permute(0, 2, 3, 1)
            return frame_tensor

        # Generate the list of coordinates for our uniform grid
        tile_coords = get_tile_coordinates(final_width, final_height, tile_width, tile_height, overlap_x, overlap_y, grid_x, grid_y)
        
        # This data is passed to the combiner node so it knows how to reassemble the tiles
        dac_data = {
            'upscaled_width': final_width,
            'upscaled_height': final_height,
            'grid_x': grid_x,
            'grid_y': grid_y,
            'overlap_x': overlap_x,
            'overlap_y': overlap_y,
            'num_frames': num_frames
        }

        tile_sequences = []
        pbar = comfy.utils.ProgressBar(len(tile_coords))
        # Iterate through each coordinate to create a tile sequence
        for tile_idx, (x, y) in enumerate(tqdm(tile_coords, desc="Simplified Grid Tiling")):
            frames_for_this_tile = []
            for frame_idx in range(num_frames):
                # Upscale the source frame once
                upscaled_source_frame = upscale_frame(images[frame_idx:frame_idx+1])
                
                # Crop the tile from the upscaled frame
                tile = upscaled_source_frame[:, y:y+tile_height, x:x+tile_width, :]
                
                # Handle cases where a tile might be smaller than the target size (at edges) by padding it
                actual_h, actual_w = tile.shape[1:3]
                if actual_h < tile_height or actual_w < tile_width:
                    padding = (0, tile_width - actual_w, 0, tile_height - actual_h)
                    tile = torch.nn.functional.pad(tile.permute(0, 3, 1, 2), padding, mode='replicate').permute(0, 2, 3, 1)
                
                frames_for_this_tile.append(tile.squeeze(0))
            
            tile_sequences.append(torch.stack(frames_for_this_tile))
            pbar.update(1)

        return (tile_sequences, dac_data)