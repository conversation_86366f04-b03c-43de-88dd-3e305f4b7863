import torch
import math
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
from tqdm import tqdm
import comfy.utils
import gc

# Import the helper function from the sibling 'tiling' module
from .tiling import get_tile_coordinates

class VideoCombineTiles:
    @classmethod
    def INPUT_TYPES(cls): 
        return {
            "required": {
                "tile_sequences": ("IMAGE",), 
                "dac_data": ("DAC_DATA",),
                "combine_on_cpu": ("BOOLEAN", {"default": False}), # --- NEW TOGGLE ---
            }
        }
    RETURN_TYPES, FUNCTION, CATEGORY, INPUT_IS_LIST = ("IMAGE",), "combine", "⚫mimikry/Upscale Utils/Tiling", True
    def combine(self, tile_sequences, dac_data, combine_on_cpu=False): # --- ADDED ARG ---
        try:
            dac_data = dac_data[0]
            upscaled_width, upscaled_height = dac_data['upscaled_width'], dac_data['upscaled_height']
            overlap_x, overlap_y, grid_x, grid_y, num_frames = dac_data['overlap_x'], dac_data['overlap_y'], dac_data['grid_x'], dac_data['grid_y'], dac_data['num_frames']
            if not tile_sequences or not isinstance(tile_sequences, list): raise TypeError("tile_sequences is not a list.")
            tile_height, tile_width = tile_sequences[0].shape[1:3]
            tile_coords = get_tile_coordinates(upscaled_width, upscaled_height, tile_width, tile_height, overlap_x, overlap_y, grid_x, grid_y)
            if len(tile_sequences) != len(tile_coords): raise ValueError(f"Tile count mismatch: received {len(tile_sequences)}, expected {len(tile_coords)}.")
            
            device = tile_sequences[0].device
            dtype = tile_sequences[0].dtype
            
            final_video_frames = []
            pbar = comfy.utils.ProgressBar(num_frames)
            for frame_idx in range(num_frames):
                frame_canvas = torch.zeros((1, upscaled_height, upscaled_width, 3), device=device, dtype=dtype)
                weight_canvas = torch.zeros((1, upscaled_height, upscaled_width, 1), device=device, dtype=dtype)
                for tile_idx, tile_video_tensor in enumerate(tile_sequences):
                    if frame_idx >= tile_video_tensor.shape[0]: continue
                    x_start, y_start = tile_coords[tile_idx]
                    mask = Image.new("L", (tile_width, tile_height), 0)
                    draw = ImageDraw.Draw(mask)
                    rect_x0, rect_y0 = (0 if x_start == 0 else overlap_x//4), (0 if y_start == 0 else overlap_y//4)
                    rect_x1, rect_y1 = (tile_width if (x_start + tile_width) >= upscaled_width else tile_width - (overlap_x//4)), (tile_height if (y_start + tile_height) >= upscaled_height else tile_height - (overlap_y//4))
                    draw.rectangle((rect_x0, rect_y0, rect_x1, rect_y1), fill=255)
                    mask = mask.filter(ImageFilter.GaussianBlur(radius=max(math.sqrt(overlap_x), math.sqrt(overlap_y))))
                    mask_tensor = torch.from_numpy(np.array(mask) / 255.0).unsqueeze(0).unsqueeze(-1).to(device, dtype=dtype)
                    tile_frame = tile_video_tensor[frame_idx:frame_idx+1]
                    h, w = tile_frame.shape[1:3]
                    frame_canvas[:, y_start:y_start+h, x_start:x_start+w, :] += tile_frame * mask_tensor[:, :h, :w, :]
                    weight_canvas[:, y_start:y_start+h, x_start:x_start+w, :] += mask_tensor[:, :h, :w, :]
                weight_canvas.clamp_(min=1e-4)
                
                # --- START OF FIX ---
                # Move the completed frame to CPU if the toggle is on
                final_frame = frame_canvas / weight_canvas
                if combine_on_cpu:
                    final_video_frames.append(final_frame.cpu())
                else:
                    final_video_frames.append(final_frame)
                # --- END OF FIX ---
                pbar.update(1)

            # This torch.cat will now happen on the CPU if the toggle was on, avoiding VRAM OOM
            return (torch.cat(final_video_frames, dim=0),)
        finally:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

class VideoCombineTiles_Advanced:
    @classmethod
    def INPUT_TYPES(cls): 
        return {
            "required": {
                "tile_sequences": ("IMAGE",), 
                "dac_data": ("DAC_DATA",),
                "combine_on_cpu": ("BOOLEAN", {"default": False, "tooltip": "Enable this for large videos to prevent VRAM errors during final combine"}), # --- NEW TOGGLE ---
            }
        }
    RETURN_TYPES,RETURN_NAMES,FUNCTION,CATEGORY,INPUT_IS_LIST = ("IMAGE","MASK"),("combined_video","subject_mask"),"combine", "⚫mimikry/Upscale Utils/Smart Tiling",True
    def combine(self, tile_sequences, dac_data, combine_on_cpu=False): # --- ADDED ARG ---
        try:
            dac_data = dac_data[0]
            upscaled_width, upscaled_height, num_frames = dac_data['upscaled_width'], dac_data['upscaled_height'], dac_data['num_frames']
            overlap_x, overlap_y = dac_data.get('overlap_x', 0), dac_data.get('overlap_y', 0)
            
            is_dynamic = 'dynamic_positions' in dac_data
            positions = dac_data['dynamic_positions'] if is_dynamic else dac_data['positions']
            tile_types = dac_data.get('tile_types', ['base'] * len(tile_sequences))
            relative_bboxes = dac_data.get('relative_bboxes', {})
            dynamic_subject_sizing = dac_data.get('dynamic_subject_sizing', False)
            subject_padding = dac_data.get('subject_padding', 0)

            device = tile_sequences[0].device
            dtype = tile_sequences[0].dtype

            final_video_frames, final_mask_frames = [], []
            
            for frame_idx in tqdm(range(num_frames), desc="Combining Advanced Tiles"):
                frame_canvas = torch.zeros((1, upscaled_height, upscaled_width, 3), dtype=dtype, device=device)
                weight_canvas = torch.zeros((1, upscaled_height, upscaled_width, 1), dtype=dtype, device=device)
                mask_canvas = torch.zeros((1, upscaled_height, upscaled_width, 1), dtype=torch.float32, device=device)

                layer_order = sorted(range(len(tile_sequences)), key=lambda i: 0 if tile_types[i] == 'base' else 1)

                for tile_idx in layer_order:
                    tile_video_tensor = tile_sequences[tile_idx]
                    if frame_idx >= tile_video_tensor.shape[0]: continue

                    tile_frame = tile_video_tensor[frame_idx:frame_idx+1]
                    pos_info = positions[tile_idx][frame_idx] if is_dynamic else positions[tile_idx]
                    current_tile_type = tile_types[tile_idx]

                    if current_tile_type == 'subject_saver' and dynamic_subject_sizing:
                        bbox_info = None
                        if isinstance(relative_bboxes, dict):
                            bbox_info = relative_bboxes.get(tile_idx)
                        elif isinstance(relative_bboxes, list):
                            num_base_tiles = tile_types.count('base')
                            track_index = tile_idx - num_base_tiles
                            if 0 <= track_index < len(relative_bboxes):
                                track_data = relative_bboxes[track_index]
                                if 0 <= frame_idx < len(track_data):
                                    bbox_info = track_data[frame_idx]
                        
                        if not bbox_info or 'tile_bbox' not in bbox_info or not bbox_info['tile_bbox']:
                            current_tile_type = 'base' 
                        else:
                            bbox_x1, bbox_y1, bbox_x2, bbox_y2 = [int(c) for c in bbox_info['tile_bbox']]
                            target_w, target_h = bbox_x2 - bbox_x1, bbox_y2 - bbox_y1
                            if target_w <= 0 or target_h <= 0: continue

                            canvas_slice_x1, canvas_slice_y1 = max(0, bbox_x1), max(0, bbox_y1)
                            canvas_slice_x2, canvas_slice_y2 = min(upscaled_width, bbox_x2), min(upscaled_height, bbox_y2)
                            canvas_slice_w, canvas_slice_h = canvas_slice_x2 - canvas_slice_x1, canvas_slice_y2 - canvas_slice_y1
                            if canvas_slice_w <= 0 or canvas_slice_h <= 0: continue

                            tile_slice_x1, tile_slice_y1 = canvas_slice_x1 - bbox_x1, canvas_slice_y1 - bbox_y1
                            tile_slice_x2, tile_slice_y2 = tile_slice_x1 + canvas_slice_w, tile_slice_y1 + canvas_slice_h

                            resized_tile_full = comfy.utils.common_upscale(tile_frame.permute(0,3,1,2), target_w, target_h, "lanczos", "center").permute(0,2,3,1)
                            mask = Image.new("L", (target_w, target_h), 255).filter(ImageFilter.GaussianBlur(radius=subject_padding))
                            new_weight_full = torch.from_numpy(np.array(mask)/255.0).unsqueeze(0).unsqueeze(-1).to(device, dtype=dtype)

                            tile_to_place = resized_tile_full[:, tile_slice_y1:tile_slice_y2, tile_slice_x1:tile_slice_x2, :]
                            new_weight = new_weight_full[:, tile_slice_y1:tile_slice_y2, tile_slice_x1:tile_slice_x2, :]

                            existing_frame_slice = frame_canvas[:, canvas_slice_y1:canvas_slice_y2, canvas_slice_x1:canvas_slice_x2, :]
                            existing_weight_slice = weight_canvas[:, canvas_slice_y1:canvas_slice_y2, canvas_slice_x1:canvas_slice_x2, :]

                            combined_weight = existing_weight_slice + new_weight
                            safe_combined_weight = combined_weight.clamp(min=1e-4)
                            blended_frame = (existing_frame_slice * existing_weight_slice + tile_to_place * new_weight) / safe_combined_weight
                            
                            frame_canvas[:, canvas_slice_y1:canvas_slice_y2, canvas_slice_x1:canvas_slice_x2, :] = blended_frame
                            weight_canvas[:, canvas_slice_y1:canvas_slice_y2, canvas_slice_x1:canvas_slice_x2, :] = combined_weight
                            if dac_data.get('subjects_only_mode', False):
                                mask_canvas[:, canvas_slice_y1:canvas_slice_y2, canvas_slice_x1:canvas_slice_x2, :] = 1.0

                    if current_tile_type == 'base': 
                        x_start, y_start = int(pos_info[0]), int(pos_info[1])
                        tile_h, tile_w = tile_frame.shape[1:3]

                        mask = Image.new("L", (tile_w, tile_h), 0)
                        draw = ImageDraw.Draw(mask)
                        rect_x0, rect_y0 = (0 if x_start == 0 else overlap_x//4), (0 if y_start == 0 else overlap_y//4)
                        rect_x1 = tile_w if (x_start + tile_w) >= upscaled_width else tile_w - (overlap_x//4)
                        rect_y1 = tile_h if (y_start + tile_h) >= upscaled_height else tile_h - (overlap_y//4)
                        draw.rectangle((rect_x0, rect_y0, rect_x1, rect_y1), fill=255)
                        blur_radius = max(math.sqrt(overlap_x), math.sqrt(overlap_y)) if overlap_x > 0 and overlap_y > 0 else 0
                        if blur_radius > 0: mask = mask.filter(ImageFilter.GaussianBlur(radius=blur_radius))
                        
                        new_weight = torch.from_numpy(np.array(mask)/255.0).unsqueeze(0).unsqueeze(-1).to(device, dtype=dtype)

                        y_end, x_end = min(y_start + tile_h, upscaled_height), min(x_start + tile_w, upscaled_width)
                        if y_end > y_start and x_end > x_start:
                            existing_frame_slice = frame_canvas[:, y_start:y_end, x_start:x_end, :]
                            existing_weight_slice = weight_canvas[:, y_start:y_end, x_start:x_end, :]
                            tile_to_place = tile_frame[:, :(y_end-y_start), :(x_end-x_start), :]
                            weight_to_add = new_weight[:, :(y_end-y_start), :(x_end-x_start), :]

                            combined_weight = existing_weight_slice + weight_to_add
                            safe_combined_weight = combined_weight.clamp(min=1e-4)
                            blended_frame = (existing_frame_slice * existing_weight_slice + tile_to_place * weight_to_add) / safe_combined_weight
                            
                            frame_canvas[:, y_start:y_end, x_start:x_end, :] = blended_frame
                            weight_canvas[:, y_start:y_end, x_start:x_end, :] = combined_weight

                # --- START OF FIX ---
                # Move the completed frames to CPU if the toggle is on
                if combine_on_cpu:
                    final_video_frames.append(frame_canvas.cpu())
                    final_mask_frames.append(mask_canvas.cpu())
                else:
                    final_video_frames.append(frame_canvas)
                    final_mask_frames.append(mask_canvas)
                # --- END OF FIX ---

            combined_video = torch.cat(final_video_frames, dim=0)
            combined_mask = torch.cat(final_mask_frames, dim=0).squeeze(-1)

            return (combined_video, combined_mask)
    
        finally:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()