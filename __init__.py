# ComfyUI_Upscale-utils/__init__.py
from .nodes.color_correction import WaveletColorFix
from .nodes.tiling import VideoDivideAndConquer
from .nodes.smart_tiling import YoloSmartTiler_Image, YoloSmartTracker_Video, MaskSmartTiler_Image, MaskSmartTracker_Video
from .nodes.reconstruction import VideoCombineTiles, VideoCombineTiles_Advanced
from .nodes.psd_export import TileMergerFromDAC, TileMergerToPSD, TileMergerFromYoloPSD

NODE_CLASS_MAPPINGS = {
    # Color Correction
    "WaveletColorFix": WaveletColorFix,
    # Tiling
    "VideoDivideAndConquer": VideoDivideAndConquer,
    "VideoCombineTiles": VideoCombineTiles, # Legacy
    # Smart Tiling & Reconstruction
    "YoloSmartTiler_Image": YoloSmartTiler_Image,
    "YoloSmartTracker_Video": YoloSmartTracker_Video,
    "MaskSmartTiler_Image": MaskSmartTiler_Image,
    "MaskSmartTracker_Video": MaskSmartTracker_Video, # New Node
    "VideoCombineTiles_Advanced": VideoCombineTiles_Advanced,
    # PSD Export
    "TileMergerFromDAC": TileMergerFromDAC,
    "TileMergerToPSD_Manual": TileMergerToPSD,
    "TileMergerFromYoloPSD": TileMergerFromYoloPSD
}

NODE_DISPLAY_NAME_MAPPINGS = {
    # Color Correction
    "WaveletColorFix": "Wavelet Color Fix",
    # Tiling
    "VideoDivideAndConquer": "Video Divide & Conquer 🎥",
    "VideoCombineTiles": "Video Combine Tiles 🎥", # Legacy
    # Smart Tiling & Reconstruction
    "YoloSmartTiler_Image": "YOLO Smart Tiler (Image) 🧠",
    "YoloSmartTracker_Video": "YOLO Smart Tracker (Video) 🧠",
    "MaskSmartTiler_Image": "Mask Smart Tiler (Image) 🧠",
    "MaskSmartTracker_Video": "Mask Smart Tracker (Video) 🧠", # New Node
    "VideoCombineTiles_Advanced": "Video Combine Tiles (Advanced) 🧠",
    # PSD Export
    "TileMergerFromDAC": "Tile Merger from DAC (Corrected)",
    "TileMergerToPSD_Manual": "Tile Merger (Manual)",
    "TileMergerFromYoloPSD": "Tile Merger from YOLO (PSD) 🧠",
}

__all__ = ["NODE_CLASS_MAPPINGS", "NODE_DISPLAY_NAME_MAPPINGS"]


print("----------------------------------")
print("### ComfyUI Upscale Utils      ###")
print("### Developed by [mimikry.ai]  ###")
print("### Version \033[34m[DEV]\033  [              ###")
print("----------------------------------")