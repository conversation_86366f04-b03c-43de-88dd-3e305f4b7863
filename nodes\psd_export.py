import torch
import math
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import os
import re
import folder_paths
import pytoshop
from pytoshop import layers
from pytoshop.enums import BlendMode, Compression
import comfy.utils

# Helper function required by TileMergerFromDAC
def _generate_blend_mask(x, y, dac_data):
    upscaled_width, upscaled_height = dac_data['upscaled_width'], dac_data['upscaled_height']
    tile_width, tile_height = dac_data['tile_width'], dac_data['tile_height']
    overlap_x, overlap_y = dac_data['overlap_x'], dac_data['overlap_y']
    overlap_factor = 4
    f_overlap_x = overlap_x // overlap_factor
    f_overlap_y = overlap_y // overlap_factor
    blend_x, blend_y = math.sqrt(overlap_x) if overlap_x > 0 else 0, math.sqrt(overlap_y) if overlap_y > 0 else 0
    mask = Image.new("L", (tile_width, tile_height), 0)
    draw = ImageDraw.Draw(mask)
    left, top = (0 if x == 0 else f_overlap_x), (0 if y == 0 else f_overlap_y)
    right, bottom = (tile_width if (x + tile_width) >= upscaled_width else (tile_width - f_overlap_x)), (tile_height if (y + tile_height) >= upscaled_height else (tile_height - f_overlap_y))
    draw.rectangle([left, top, right, bottom], fill=255)
    blur_radius = max(blend_x, blend_y)
    if blur_radius > 0:
        mask = mask.filter(ImageFilter.GaussianBlur(radius=blur_radius))
    return np.array(mask)

# Helper function required by TileMergerFromDAC
def create_tile_coordinates(image_width, image_height, tile_width, tile_height, overlap_x, overlap_y, grid_x, grid_y, tile_order):
    linear_tiles = []
    for row in range(grid_y):
        y = row * (tile_height - overlap_y)
        if row == grid_y - 1: y = image_height - tile_height
        for col in range(grid_x):
            x = col * (tile_width - overlap_x)
            if col == grid_x - 1: x = image_width - tile_width
            linear_tiles.append((int(x), int(y)))
    return linear_tiles, None

class TileMergerFromDAC:
    def __init__(self): self.output_dir = folder_paths.get_output_directory(); self.type = "output"
    @classmethod
    def INPUT_TYPES(cls): return {"required": {"images":("IMAGE",),"dac_data":("DAC_DATA",),"filename_prefix":("STRING",{"default":"TiledPSD_Final"}),"apply_blend_mask":("BOOLEAN",{"default":True}),}}
    RETURN_TYPES,FUNCTION,OUTPUT_NODE,CATEGORY,INPUT_IS_LIST = (),"execute",True,"⚫mimikry/Upscale Utils/PSD",True
    def execute(self, images, dac_data, filename_prefix, apply_blend_mask):
        if isinstance(filename_prefix,list):filename_prefix=filename_prefix[0]
        if isinstance(apply_blend_mask,list):apply_blend_mask=apply_blend_mask[0]
        dac_data=dac_data[0]
        images=torch.cat(images,dim=0)
        _batch_size,tile_h,tile_w,_ = images.shape
        dac_data['tile_width'],dac_data['tile_height']=tile_w,tile_h
        try:
            tile_positions,_=create_tile_coordinates(dac_data['upscaled_width'],dac_data['upscaled_height'],dac_data['tile_width'],dac_data['tile_height'],dac_data['overlap_x'],dac_data['overlap_y'],dac_data['grid_x'],dac_data['grid_y'],dac_data.get('tile_order',0))
            canvas_w,canvas_h=dac_data['upscaled_width'],dac_data['upscaled_height']
            batch_size,img_h,img_w,_=images.shape
            if len(tile_positions)!=batch_size:print(f"Warning: Pos count ({len(tile_positions)}) != image batch size ({batch_size}).")
        except Exception as e:print(f"FATAL: Error processing DAC data: {e}");return {}
        psd=pytoshop.core.PsdFile(num_channels=4,height=canvas_h,width=canvas_w)
        layer_records=[]
        for i in range(min(batch_size,len(tile_positions))):
            x,y=tile_positions[i];tile_tensor=images[i]
            if tile_tensor.shape[2]!=4:tile_tensor=torch.cat((tile_tensor[...,:3],torch.ones(img_h,img_w,1,device=tile_tensor.device)),dim=2)
            tile_numpy=(tile_tensor.cpu().numpy()*255).astype(np.uint8)
            channels={-1:layers.ChannelImageData(image=tile_numpy[:,:,3],compression=Compression.raw),0:layers.ChannelImageData(image=tile_numpy[:,:,0],compression=Compression.raw),1:layers.ChannelImageData(image=tile_numpy[:,:,1],compression=Compression.raw),2:layers.ChannelImageData(image=tile_numpy[:,:,2],compression=Compression.raw),}
            layer_mask=None
            if apply_blend_mask:
                mask_numpy=_generate_blend_mask(x,y,dac_data)
                channels[-2]=layers.ChannelImageData(image=mask_numpy,compression=Compression.raw)
                layer_mask=layers.LayerMask(top=y,left=x,right=x+img_w,bottom=y+img_h)
            layer_record=layers.LayerRecord(top=y,left=x,bottom=y+img_h,right=x+img_w,channels=channels,blend_mode=BlendMode.normal,name=f"Tile_{i+1}",opacity=255)
            if layer_mask:layer_record.mask=layer_mask
            layer_records.append(layer_record)
        psd.layer_and_mask_info.layer_info.layer_records=layer_records
        full_output_folder,filename,counter,_,_=folder_paths.get_save_image_path(filename_prefix,self.output_dir,canvas_w,canvas_h)
        output_path=os.path.join(full_output_folder,f"{filename}_{counter:05}_.psd")
        try:
            with open(output_path,'wb')as fd:psd.write(fd)
        except Exception as e:print(f"Error saving PSD file: {e}");return {}
        return {"ui":{"result":[f"Saved to {output_path}"]}}

class TileMergerToPSD:
    def __init__(self):self.output_dir=folder_paths.get_output_directory();self.type="output"
    @classmethod
    def INPUT_TYPES(cls):return{"required":{"images":("IMAGE",),"tile_coords":("STRING",{"multiline":True}),"filename_prefix":("STRING",{"default":"TiledPSD_Manual"})},"optional":{"canvas_width":("INT",{"default":0,"min":0}),"canvas_height":("INT",{"default":0,"min":0})}}
    RETURN_TYPES,FUNCTION,OUTPUT_NODE,CATEGORY=(),"merge_tiles_to_psd",True,"⚫mimikry/Upscale Utils/PSD"
    def merge_tiles_to_psd(self,images,tile_coords,filename_prefix,canvas_width=0,canvas_height=0):
        coords_map={};batch_size,tile_height,tile_width,_=images.shape
        try:
            for index_str,x_str,y_str in re.findall(r'(\d+)\s*\(\s*(\d+)\s*,\s*(\d+)\s*\)',tile_coords):coords_map[int(index_str)]=(int(x_str),int(y_str))
        except Exception as e:print(f"Error parsing tile_coords: {e}");return{}
        if canvas_width*canvas_height==0:
            if not coords_map:print("Error: Cannot auto-calculate canvas size.");return{}
            max_x,max_y=max(x for x,y in coords_map.values()),max(y for x,y in coords_map.values())
            canvas_width,canvas_height=max_x+tile_width,max_y+tile_height
        psd=pytoshop.core.PsdFile(num_channels=4,height=canvas_height,width=canvas_width)
        layer_records=[]
        for i in range(batch_size):
            tile_index=i+1
            if tile_index not in coords_map:continue
            x,y=coords_map[tile_index];tile_tensor=images[i]
            if tile_tensor.shape[2]!=4:tile_tensor=torch.cat((tile_tensor[...,:3],torch.ones(tile_height,tile_width,1,device=tile_tensor.device)),dim=2)
            layer_data=[layers.ChannelImageData(image=(tile_tensor.cpu().numpy()*255).astype(np.uint8)[:,:,ch],compression=Compression.raw)for ch in range(4)]
            layer_records.append(layers.LayerRecord(channels={-1:layer_data[3],0:layer_data[0],1:layer_data[1],2:layer_data[2]},top=y,left=x,bottom=y+tile_height,right=x+tile_width,blend_mode=BlendMode.normal,name=f"Tile_{tile_index}",opacity=255))
        psd.layer_and_mask_info.layer_info.layer_records=layer_records;full_output_folder,filename,counter,_,_=folder_paths.get_save_image_path(filename_prefix,self.output_dir,canvas_width,canvas_height);output_path=os.path.join(full_output_folder,f"{filename}_{counter:05}_.psd")
        try:
            with open(output_path,'wb')as fd:psd.write(fd)
        except Exception as e:print(f"Error saving PSD file: {e}");return{}
        return {"ui":{"result":[f"Saved to {output_path}"]}}

class TileMergerFromYoloPSD:
    def __init__(self): self.output_dir = folder_paths.get_output_directory(); self.type = "output"
    @classmethod
    def INPUT_TYPES(cls): return {"required": { "images": ("IMAGE",), "yolo_dac_data": ("DAC_DATA",), "filename_prefix": ("STRING", {"default": "YoloTiledPSD_Final"}), "apply_blend_mask": ("BOOLEAN", {"default": True}),}}
    RETURN_TYPES,FUNCTION,OUTPUT_NODE,CATEGORY,INPUT_IS_LIST = (),"execute",True,"⚫mimikry/Upscale Utils/PSD",True
    def execute(self, images, yolo_dac_data, filename_prefix, apply_blend_mask):
        if isinstance(filename_prefix, list): filename_prefix = filename_prefix[0]
        dac_data = yolo_dac_data[0]
        images = torch.cat(images, dim=0)
        canvas_w, canvas_h = dac_data['upscaled_width'], dac_data['upscaled_height']
        positions = dac_data.get('positions', [])
        
        # Get original tile dimensions before any resizing happens
        tile_h_orig, tile_w_orig = images.shape[1:3]
        
        if not positions: raise ValueError("'positions' not found in dac_data.")
        psd = pytoshop.core.PsdFile(num_channels=4, height=canvas_h, width=canvas_w)
        layer_records = []
        
        tile_types = dac_data.get('tile_types', ['base'] * len(images))
        relative_bboxes = dac_data.get('relative_bboxes', {})
        dynamic_subject_sizing = dac_data.get('dynamic_subject_sizing', False)
        subject_padding = dac_data.get('subject_padding', 0)

        for i, tile_tensor in enumerate(images):
            x, y = positions[i]
            img_h, img_w = tile_h_orig, tile_w_orig
            current_tile_type = tile_types[i] if i < len(tile_types) else 'base'

            if current_tile_type == 'subject_saver' and dynamic_subject_sizing and i in relative_bboxes:
                bbox_info = relative_bboxes[i]
                if 'tile_bbox' in bbox_info and bbox_info['tile_bbox']:
                    tile_bbox_x1, tile_bbox_y1, tile_bbox_x2, tile_bbox_y2 = [int(c) for c in bbox_info['tile_bbox']]
                    bbox_width, bbox_height = tile_bbox_x2 - tile_bbox_x1, tile_bbox_y2 - tile_bbox_y1

                    if bbox_width > 0 and bbox_height > 0:
                        scaled_tile = comfy.utils.common_upscale(tile_tensor.unsqueeze(0).permute(0,3,1,2), bbox_width, bbox_height, "lanczos", "center").permute(0,2,3,1).squeeze(0)
                        x, y = tile_bbox_x1, tile_bbox_y1
                        img_h, img_w = bbox_height, bbox_width
                        tile_tensor = scaled_tile

            if tile_tensor.shape[2]!=4: tile_tensor=torch.cat((tile_tensor[...,:3],torch.ones(img_h,img_w,1,device=tile_tensor.device)),dim=2)
            tile_numpy = (tile_tensor.cpu().numpy()*255).astype(np.uint8)
            channels = {-1:layers.ChannelImageData(image=tile_numpy[:,:,3]), 0:layers.ChannelImageData(image=tile_numpy[:,:,0]), 1:layers.ChannelImageData(image=tile_numpy[:,:,1]), 2:layers.ChannelImageData(image=tile_numpy[:,:,2])}

            layer_mask = None
            if apply_blend_mask:
                mask = Image.new("L", (img_w, img_h), 0)
                draw = ImageDraw.Draw(mask)
                
                if current_tile_type == 'subject_saver' and i in relative_bboxes:
                    # --- MASK SCALING FIX STARTS HERE ---
                    bbox_info = relative_bboxes[i]
                    
                    # Calculate scaling factors from original tile to resized tile
                    scale_w = img_w / tile_w_orig if tile_w_orig > 0 else 1
                    scale_h = img_h / tile_h_orig if tile_h_orig > 0 else 1
                    
                    # Scale the relative bbox coordinates and padding
                    scaled_rel_x1 = bbox_info['x1'] * scale_w
                    scaled_rel_y1 = bbox_info['y1'] * scale_h
                    scaled_rel_x2 = bbox_info['x2'] * scale_w
                    scaled_rel_y2 = bbox_info['y2'] * scale_h
                    scaled_padding = subject_padding * min(scale_w, scale_h)

                    padded_bbox = (
                        max(0, scaled_rel_x1 - scaled_padding),
                        max(0, scaled_rel_y1 - scaled_padding),
                        min(img_w, scaled_rel_x2 + scaled_padding),
                        min(img_h, scaled_rel_y2 + scaled_padding)
                    )
                    draw.rectangle(padded_bbox, fill=255)
                    if scaled_padding > 0:
                        mask = mask.filter(ImageFilter.GaussianBlur(radius=scaled_padding))
                    # --- MASK SCALING FIX ENDS HERE ---
                else:
                    # Standard feathering for base tiles
                    mask_data = {'upscaled_width': canvas_w, 'upscaled_height': canvas_h, 'tile_width': img_w, 'tile_height': img_h, 'overlap_x': dac_data.get('overlap_x', 0), 'overlap_y': dac_data.get('overlap_y', 0)}
                    mask = Image.fromarray(_generate_blend_mask(x, y, mask_data))

                channels[-2] = layers.ChannelImageData(image=np.array(mask))
                layer_mask = layers.LayerMask(top=y, left=x, right=x+img_w, bottom=y+img_h)

            layer_record = layers.LayerRecord(top=int(y), left=int(x), bottom=int(y+img_h), right=int(x+img_w), channels=channels, name=f"Tile_{i+1}")
            if layer_mask: layer_record.mask = layer_mask
            layer_records.append(layer_record)

        base_layers = [lr for i, lr in enumerate(layer_records) if tile_types[i] == 'base']
        subject_layers = [lr for i, lr in enumerate(layer_records) if tile_types[i] != 'base']
        psd.layer_and_mask_info.layer_info.layer_records = base_layers + subject_layers

        full_output_folder,filename,counter,_,_=folder_paths.get_save_image_path(filename_prefix, self.output_dir, canvas_w, canvas_h)
        output_path=os.path.join(full_output_folder,f"{filename}_{counter:05}_.psd")
        with open(output_path,'wb') as fd: psd.write(fd)
        return {"ui": {"result": [f"Saved to {output_path}"]}}