# ComfyUI Upscale Utils

This is a comprehensive node pack for ComfyUI that combines several powerful utilities for video and image upscaling workflows. It merges tools for color correction, memory-efficient video tiling, and exporting tiled results to layered PSD files.

## Features

- **🧠 YOLO Smart Tiling & Tracking**: AI-powered intelligent tiling that detects and follows subjects
- **🎯 Dynamic Subject Sizing**: Automatically resizes tiles around detected subjects for optimal quality
- **🚀 Memory-Efficient Processing**: Frame-by-frame processing prevents RAM exhaustion on large videos
- **🎨 Custom Model Support**: Full integration with any ComfyUI-compatible upscale model
- **📐 Exact Dimension Control**: Guaranteed precise tile dimensions across all frames
- **🎥 Advanced Video Tracking**: YOLO-based object tracking that follows subjects through video
- **🔧 Hybrid Grid Algorithm**: Combines guaranteed coverage with intelligent subject-focused tiling
- **📊 Clean Subject Masks**: Precise mask output for advanced compositing workflows
- **🎭 Layered PSD Export**: Professional-grade PSD files with proper layer organization
- **🐛 Advanced Debugging**: Comprehensive visualization tools for fine-tuning workflows

## Installation

1.  Navigate to your `ComfyUI/custom_nodes/` directory.
2.  Clone this repository:
    ```bash
    git clone <repository-url> ComfyUI_Upscale-utils
    ```
3.  Activate your ComfyUI's Python virtual environment and install the required dependencies:
    ```sh
    pip install -r custom_nodes/ComfyUI_Upscale-utils/requirements.txt
    ```
4.  **Install Recommended Nodes**: For video processing, it is highly recommended to install [ComfyUI-VideoHelperSuite](https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite).
5.  **YOLO Models**: The nodes support 35+ YOLO model variants (YOLO11, YOLOv10, YOLOv9, YOLOv8, YOLOv5) and will automatically download them to `ComfyUI/models/yolo/` on first use.
6.  Restart ComfyUI.

## 🚀 Latest Updates

### Enhanced YOLO Model Support
- **YOLO11**: Latest state-of-the-art models with 22% fewer parameters and better accuracy
- **YOLOv10**: Efficient NMS-free detection models
- **YOLOv9**: Advanced architecture with Programmable Gradient Information
- **Segmentation Models**: Better object boundary detection with `-seg` variants
- **35+ Model Variants**: From nano (1.5ms) to XLarge (maximum accuracy)
- **Smart Defaults**: YOLO11-Medium selected for optimal balance

## Quick Start Workflows

### Basic Image Upscaling with Smart Tiling
```
Load Image → YOLO Smart Tiler (Image) → [Process Tiles] → Video Combine Tiles (Advanced) → Save Image
```

### Video Upscaling with Subject Tracking
```
Load Video → YOLO Smart Tracker (Video) → [Process Tiles] → Video Combine Tiles (Advanced) → Save Video
```

### Professional PSD Export
```
YOLO Smart Tiler/Tracker → [Process Tiles] → Tile Merger from YOLO PSD → [PSD File Output]
```

### Custom Model Integration
```
Load Image/Video → YOLO Smart Tiler/Tracker (with upscale_model) → Video Combine Tiles (Advanced)
```

## Nodes Included

---

### Smart Tiling & Tracking 🧠

The smart tiling functionality features two powerful, specialized nodes with advanced AI-powered capabilities.

#### 1. YOLO Smart Tiler (Image) 🧠
**Memory-efficient image processing with intelligent subject detection**
- **Hybrid Grid Algorithm**: Combines guaranteed coverage with smart subject-focused tiling
- **Dynamic Subject Sizing**: Automatically calculates optimal tile sizes around detected subjects
- **Custom Model Support**: Full integration with any ComfyUI upscale model
- **Memory Optimization**: Frame-by-frame processing prevents RAM exhaustion
- **Exact Dimensions**: Guaranteed precise tile dimensions with validation
- **Advanced AI Detection**: Uses latest YOLO models (YOLO11, YOLOv10, YOLOv9, YOLOv8, YOLOv5) with 35+ model variants
- **Clean Masks**: Outputs precise subject masks for advanced compositing

**Key Features:**
- `dynamic_subject_sizing`: Automatically resizes tiles around subjects for optimal quality
- `subject_padding`: Configurable padding around detected subjects
- `subjects_only_mode`: Option to output only subject tiles, skipping base grid
- `upscale_model`: Optional custom upscale model support
- `debug_output`: Visual debugging showing tile placement and subject detection

#### 2. YOLO Smart Tracker (Video) 🧠
**Advanced video processing with object tracking and dynamic tile positioning**
- **Object Tracking**: Uses advanced YOLO models with ByteTrack to follow subjects across frames
- **Dynamic Positioning**: Tiles automatically move to follow tracked subjects
- **Memory Efficient**: Processes frames individually to handle large videos
- **Consistent Dimensions**: Ensures exact tile dimensions across all frames
- **Custom Model Support**: Full upscale model integration with frame-by-frame processing

**Key Features:**
- `filter_classes`: Specify subjects to track (e.g., "person, car, dog")
- `dynamic_subject_sizing`: Consistent tile-bbox sizing across entire tracks
- `confidence`: Adjustable detection confidence threshold
- `debug_mode`:
  - `disabled`: No debug output
  - `first_frame`: Shows layout on first frame
  - `full_video`: Complete tracking visualization
- `subjects_only_mode`: Track-only output option

---

### Advanced Reconstruction Nodes

#### 1. Video Combine Tiles (Advanced) 🧠
**Intelligent video reconstruction with upscale model support**
- **Universal Compatibility**: Works seamlessly with both Image and Video YOLO nodes
- **Dynamic Scaling**: Automatically handles dimension differences from upscale models
- **Smart Data Detection**: Automatically detects image vs video data structures
- **Precise Placement**: Correctly positions dynamic subject tiles with scaling
- **Clean Subject Masks**: Outputs precise masks showing tile-bbox regions
- **Priority Blending**: Advanced feathering ensures seamless tile integration

**Key Features:**
- Automatic upscale model dimension compensation
- Frame-by-frame processing for memory efficiency
- Clean subject mask output for compositing workflows
- Perfect tile-bbox placement regardless of source node

#### 2. Tile Merger from YOLO PSD 🎨
**Professional PSD export with intelligent layer organization**
- **Smart Feathering**: Uses the same high-quality blending as DAC (Corrected) node
- **Layer Priority**: Subject tiles automatically layered above base tiles
- **Upscale Model Support**: Proper scaling and positioning for all scenarios
- **Memory Efficient**: Optimized processing for large tile sets
- **Professional Output**: Industry-standard PSD files with proper layer naming

---

### Core Utilities

#### Video Divide & Conquer 🎥
**Memory-efficient uniform grid tiling**
- **Custom Model Support**: Full upscale model integration
- **Memory Optimization**: Frame-by-frame processing
- **Flexible Scaling**: Multiple upscaling methods including custom models

#### Wavelet Color Fix 🎨
**Advanced color correction and transfer**
- **Wavelet-based**: Preserves detail while transferring color
- **AdaIN Support**: Alternative color transfer method
- **Video Compatible**: Works with both images and video sequences

#### Legacy Nodes
- **Video Combine Tiles 🎥**: Original video reconstruction node
- **Tile Merger from DAC**: Original PSD export functionality
- **Tile Merger to PSD**: Manual tile arrangement to PSD

---

## Contributing

We welcome contributions! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with various image/video sizes
5. Submit a pull request

---

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **YOLOv8**: Ultralytics for the excellent object detection and tracking models
- **ComfyUI**: For the amazing node-based interface and ecosystem
- **Community**: All the users who provided feedback and testing